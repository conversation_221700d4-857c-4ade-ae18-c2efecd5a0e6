package com.maguo.loan.cash.flow.entrance.common.service;

import com.jinghang.cash.api.dto.ProjectInfoDto;
import com.maguo.loan.cash.flow.entity.vo.ProjectInfoVO;
import com.maguo.loan.cash.flow.remote.manage.ProjectInfoFeign;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 项目相关服务
 *
 * @Author: Lior
 * @CreateTime: 2025/8/18 14:11
 */
@Service
public class ProjectInfoService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectInfoService.class);

    @Autowired
    private ProjectInfoFeign projectInfoFeign;

    /**
     * 根据项目编码查询项目完整信息（带缓存）
     *
     * @param projectCode 项目编码
     * @return 项目完整信息VO
     */
    public ProjectInfoVO queryProjectInfo(String projectCode) {
        if (StringUtils.isBlank(projectCode)) {
            logger.info("项目编码为空，无法查询项目信息");
            return null;
        }
        ProjectInfoVO vo = new ProjectInfoVO();
        ProjectInfoDto projectInfoDto = projectInfoFeign.queryProjectInfo(projectCode);
        BeanUtils.copyProperties(projectInfoDto, vo);
        return vo;
    }
}
