/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.project.service.impl;

import com.github.pagehelper.PageInfo;
import com.jinghang.cash.modules.manage.RestResult;
import com.jinghang.cash.modules.project.domain.ProjectContractConfig;

import lombok.RequiredArgsConstructor;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghang.cash.modules.project.service.service.ProjectContractService;
import com.jinghang.cash.modules.project.domain.dto.ProjectContractQueryCriteria;
import com.jinghang.cash.modules.project.mapper.ProjectContractMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @description 服务实现
* <AUTHOR>
* @date 2025-08-21
**/
@Service
@RequiredArgsConstructor
public class ProjectContractServiceImpl extends ServiceImpl<ProjectContractMapper, ProjectContractConfig> implements ProjectContractService {

    private final ProjectContractMapper projectContractConfigMapper;

  /*  @Override
    public PageInfo<ProjectContractConfig> queryAll(ProjectContractQueryCriteria criteria, Page<Object> page){
        return PageUtil.toPage(projectContractConfigMapper.findAll(criteria, page));
    }*/

    @Override
    public PageInfo queryAll(ProjectContractQueryCriteria criteria, Page<Object> page) {
        return null;
    }

    @Override
    public List<ProjectContractConfig> queryAll(ProjectContractQueryCriteria criteria){
        return projectContractConfigMapper.findAll(criteria);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(ProjectContractConfig resources) {
        projectContractConfigMapper.insert(resources);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProjectContractConfig resources) {
        ProjectContractConfig projectContractConfig = getById(resources.getId());
        projectContractConfig.copy(resources);
        projectContractConfigMapper.updateById(projectContractConfig);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAll(List<String> ids) {
        projectContractConfigMapper.deleteBatchIds(ids);
    }

    @Override
    public void download(List<ProjectContractConfig> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ProjectContractConfig projectContractConfig : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("项目唯一编码", projectContractConfig.getProjectCode());
            map.put("合同模板唯一编码", projectContractConfig.getContractCode());
            map.put("合同英文简称(文件名)", projectContractConfig.getContractFileName());
            map.put("合同模板中文描述", projectContractConfig.getContractDescription());
            map.put("合同归属方  FLOW-资产; BANK-资金 GUARANTEE-融担", projectContractConfig.getContractOwner());
            map.put("甲方", projectContractConfig.getPartyA());
            map.put("乙方", projectContractConfig.getPartyB());
            map.put("合同开始时间", projectContractConfig.getContractStartTime());
            map.put("合同结束时间", projectContractConfig.getContractEndTime());
            map.put("启用状态 INIT未生效 Z生效 3 失效", projectContractConfig.getEnabled());
            map.put("创建人", projectContractConfig.getCreateBy());
            map.put("创建时间", projectContractConfig.getCreateTime());
            map.put("更新人", projectContractConfig.getUpdateBy());
            map.put("更新时间", projectContractConfig.getUpdateTime());
            map.put("文件类型", projectContractConfig.getFileType());
            map.put("文件扩展名", projectContractConfig.getExtension());
            map.put("扩展字段1", projectContractConfig.getExt1());
            map.put("扩展字段2", projectContractConfig.getExt2());
            list.add(map);
        }
        //FileUtil.downloadExcel(list, response);
    }
}