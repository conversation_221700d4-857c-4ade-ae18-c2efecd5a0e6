/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.project.rest;

import com.github.pagehelper.PageInfo;
import com.jinghang.cash.modules.manage.RestResult;
import com.jinghang.cash.modules.project.domain.ProjectContractConfig;
import com.jinghang.cash.modules.project.service.service.ProjectContractService;
import com.jinghang.cash.modules.project.domain.dto.ProjectContractQueryCriteria;
import lombok.RequiredArgsConstructor;
import java.util.List;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
* <AUTHOR>
* @date 2025-08-21
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "合同")
@RequestMapping("/api/projectContractConfig")
public class ProjectContractController {

    private final ProjectContractService projectContractConfigService;

    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('projectContractConfig:list')")
    public void exportProjectContractConfig(HttpServletResponse response, ProjectContractQueryCriteria criteria) throws IOException {
        projectContractConfigService.download(projectContractConfigService.queryAll(criteria), response);
    }

    @GetMapping
    @ApiOperation("查询合同")
    @PreAuthorize("@el.check('projectContractConfig:list')")
    public RestResult<PageInfo<ProjectContractConfig>> queryProjectContractConfig(ProjectContractQueryCriteria criteria){
        Page<Object> page = new Page<>(criteria.getPage(), criteria.getSize());
        return RestResult.success(projectContractConfigService.queryAll(criteria, page));
    }

    @PostMapping
    @ApiOperation("新增合同")
    @PreAuthorize("@el.check('projectContractConfig:add')")
    public RestResult<Object> createProjectContractConfig(@Validated @RequestBody ProjectContractConfig resources){
        projectContractConfigService.create(resources);
        return RestResult.success(HttpStatus.CREATED);
    }

    @PutMapping
    @ApiOperation("修改合同")
    @PreAuthorize("@el.check('projectContractConfig:edit')")
    public RestResult<Object> updateProjectContractConfig(@Validated @RequestBody ProjectContractConfig resources){
        projectContractConfigService.update(resources);
        return RestResult.success(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @ApiOperation("删除合同")
    @PreAuthorize("@el.check('projectContractConfig:del')")
    public RestResult<Object> deleteProjectContractConfig(@ApiParam(value = "传ID数组[]") @RequestBody List<String> ids) {
        projectContractConfigService.deleteAll(ids);
        return RestResult.success(HttpStatus.OK);
    }
}