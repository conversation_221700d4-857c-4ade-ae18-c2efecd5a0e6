<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jinghang.cash.modules.project.mapper.ProjectContractMapper">
    <resultMap id="BaseResultMap" type="com.jinghang.cash.modules.project.domain.ProjectContractConfig">
        <id column="id" property="id"/>
        <result column="project_code" property="projectCode"/>
        <result column="contract_code" property="contractCode"/>
        <result column="contract_file_name" property="contractFileName"/>
        <result column="contract_description" property="contractDescription"/>
        <result column="contract_owner" property="contractOwner"/>
        <result column="party_a" property="partyA"/>
        <result column="party_b" property="partyB"/>
        <result column="contract_start_time" property="contractStartTime"/>
        <result column="contract_end_time" property="contractEndTime"/>
        <result column="enabled" property="enabled"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="file_type" property="fileType"/>
        <result column="extension" property="extension"/>
        <result column="ext1" property="ext1"/>
        <result column="ext2" property="ext2"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, project_code, contract_code, contract_file_name, contract_description, contract_owner, party_a, party_b, contract_start_time, contract_end_time, enabled, create_by, create_time, update_by, update_time, file_type, extension, ext1, ext2
    </sql>

    <select id="findAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from project_contract_config
        <where>
        </where>
        order by id desc
    </select>
</mapper>